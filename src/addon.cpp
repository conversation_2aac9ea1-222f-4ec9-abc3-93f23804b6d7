#include <node.h>
#include <v8.h>
#include <iostream>
#include "video_processor_wrapper.h"
#include "logging/logging.h"

namespace VideoDecodingAddon {

using v8::Context;
using v8::Function;
using v8::FunctionCallbackInfo;
using v8::Isolate;
using v8::Local;
using v8::Object;
using v8::String;
using v8::Value;
using v8::Boolean;
using v8::Exception;

void createIQVideoProcessor(const FunctionCallbackInfo<Value>& args) {
  Isolate* isolate = args.GetIsolate();
  // Check that we have exactly 1 argument (configuration object)
  if (args.Length() != 1) {
    isolate->ThrowException(Exception::TypeError(String::NewFromUtf8(isolate, "expected exactly 1 argument: configuration object").ToLocalChecked()));
    return;
  }
  // Validate that the argument is an object
  if (!args[0]->IsObject()) {
    isolate->ThrowException(Exception::TypeError(String::NewFromUtf8(isolate, "argument must be a configuration object").ToLocalChecked()));
    return;
  }

  // Create a new VideoProcessorWrapper instance
  const Local<Context> context = isolate->GetCurrentContext();
  const Local<Function> constructor = Local<Function>::New(isolate, VideoProcessorWrapper::constructor);
  const Local<Object> instance = constructor->NewInstance(context, 0, nullptr).ToLocalChecked();

  try {
    // Get the configuration object
    const Local<Object> configObject = Local<Object>::Cast(args[0]);
    // Get the wrapper object and call start
    auto* wrapper = node::ObjectWrap::Unwrap<VideoProcessorWrapper>(instance);
    const auto started = wrapper->start(configObject);

    // Return the instance or null if start failed
    started
      ? args.GetReturnValue().Set(instance)
      : args.GetReturnValue().Set(Null(isolate));
  } catch (const std::exception& e) {
    const std::string message = e.what();
    LOG_ERROR(NODE_ADDON, "" << message);

    isolate->ThrowException(Exception::Error(String::NewFromUtf8(isolate, ("addon: " + message).c_str()).ToLocalChecked()));
  }
}

void setLogLevel(const FunctionCallbackInfo<Value>& args) {
  Isolate* isolate = args.GetIsolate();
  if (args.Length() != 2) {
    isolate->ThrowException(Exception::TypeError(String::NewFromUtf8(isolate, "expected exactly 2 arguments: logger identifier and log level").ToLocalChecked()));
    return;
  }
  if (!args[0]->IsNumber() || !args[1]->IsNumber()) {
    isolate->ThrowException(Exception::TypeError(String::NewFromUtf8(isolate, "both arguments must be numbers").ToLocalChecked()));
    return;
  }

  const int loggerTypeValue = args[0]->Int32Value(isolate->GetCurrentContext()).FromJust();
  const int logLevelValue = args[1]->Int32Value(isolate->GetCurrentContext()).FromJust();
  if (loggerTypeValue < 0 || loggerTypeValue >= Logging::getLoggerTypeCount()) {
    isolate->ThrowException(Exception::RangeError(String::NewFromUtf8(isolate, "logger identifier out of valid range").ToLocalChecked()));
    return;
  }
  // Validate log level range
  if (logLevelValue < static_cast<int>(Logging::LogLevel::NONE) || logLevelValue > static_cast<int>(Logging::LogLevel::VERBOSE)) {
    isolate->ThrowException(Exception::RangeError(String::NewFromUtf8(isolate, "log level out of valid range").ToLocalChecked()));
    return;
  }

  const auto loggerType = static_cast<Logging::LoggerType>(loggerTypeValue);
  const auto logLevel = static_cast<Logging::LogLevel>(logLevelValue);
  Logging::setLogLevel(loggerType, logLevel);
  args.GetReturnValue().Set(Boolean::New(isolate, true));
}

/**
 * Initialize the addon
 */
void Initialize(const Local<Object> exports) {
  VideoProcessorWrapper::Node_Init(exports);
  NODE_SET_METHOD(exports, "createIQVideoProcessor", createIQVideoProcessor);
  NODE_SET_METHOD(exports, "setLogLevel", setLogLevel);
}

NODE_MODULE(NODE_GYP_MODULE_NAME, Initialize)

}
