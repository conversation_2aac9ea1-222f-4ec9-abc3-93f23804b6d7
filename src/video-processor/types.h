#pragma once
#include <tuple>

namespace IQVideoProcessor {

enum VideoStandard {
  UNKNOWN_VIDEO_STANDARD = 0,
  NTSC_STANDARD = 10,
  PAL_STANDARD = 20,
  PAL_INTERLACED_STANDARD = 30,
  SECAM_STANDARD = 40,
};

inline bool isVideoStandardInterlaced(const VideoStandard standard) {
  switch(standard) {
    case NTSC_STANDARD:
    case PAL_INTERLACED_STANDARD:
      return true;
    case PAL_STANDARD:
    case SECAM_STANDARD:
    case UNKNOWN_VIDEO_STANDARD:
    default:
      return false;
  }
}

inline std::tuple<size_t, size_t> getVideoStandardDimensions(const VideoStandard standard) {
  size_t width, height;
  switch (standard) {
  case NTSC_STANDARD:
    // NTSC: 720x480 (standard digital resolution)
    width = 720;
    height = 480;
    break;

  case PAL_STANDARD:
  case PAL_INTERLACED_STANDARD:
  case SECAM_STANDARD:
    // PAL: 720x576 (standard digital resolution)
    width = 720;
    height = 576;
    break;

  case UNKNOWN_VIDEO_STANDARD:
  default:
    // Default fallback dimensions
    width = 720;
    height = 480;
    break;
  }
  return { width, height };
}

// Returns left and right padding in seconds
inline std::tuple<double, double> getVideoStandardPaddings(const VideoStandard standard) {
  double leftPadding, rightPadding;
  switch (standard) {
  case NTSC_STANDARD:
  case PAL_STANDARD:
  case PAL_INTERLACED_STANDARD:
  case SECAM_STANDARD:
  case UNKNOWN_VIDEO_STANDARD:
  default:
    // PAL: 720x576 (standard digital resolution)
    leftPadding = 6.6e-6;  // 6.6 µs
    rightPadding = 3.5e-6;  // 3.5 µs
    break;
  }
  return { leftPadding, rightPadding };
}

}
