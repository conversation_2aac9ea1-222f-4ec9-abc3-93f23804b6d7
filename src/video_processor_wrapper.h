#pragma once
#include <node_object_wrap.h>
#include <v8.h>
#include <uv.h>
#include <memory>
#include "video-processor/video_processor.h"
#include "./node/thread_runner.h"

namespace VideoDecodingAddon {

/**
 * VideoProcessorWrapper - Node.js object wrapper for IQVideoProcessor::VideoProcessor
 * This class allows each JavaScript object to own and manage its own C++ VideoProcessor instance
 */
class VideoProcessorWrapper final : public node::ObjectWrap {
public:
  // Node-specific methods / exports / prototypes
  static void Node_Init(v8::Local<v8::Object> exports);
  static void Node_New(const v8::FunctionCallbackInfo<v8::Value>& args);
  static void Stop_Prototype(const v8::FunctionCallbackInfo<v8::Value>& args);
  static v8::Persistent<v8::Function> constructor;

  VideoProcessorWrapper() = default;
  ~VideoProcessorWrapper() override;

  [[nodiscard]] bool start(v8::Local<v8::Object> configObject);

  void stop();
  void stop(int code);

private:
  bool initializeNodeCallbacks(const v8::Local<v8::Object>& configObject);
  [[nodiscard]] std::unique_ptr<IIQStream> createIQStream(const v8::Local<v8::Object>& configObject) const;

  // Instance members
  std::unique_ptr<IQVideoProcessor::VideoProcessor> videoProcessor_;
  std::unique_ptr<NodeHelpers::NodeThreadRunner> nodeRunner_;

  // Callback storage for JavaScript callbacks
  v8::Persistent<v8::Function> onEventCallback_;
  v8::Persistent<v8::Function> onStopCallback_;
  v8::Persistent<v8::Function> onErrorCallback_;
  v8::Persistent<v8::Function> onFrameCallback_;

  void emitFramesToJS() const;
  void emitStopEventToJS(int code) const;


  void processEventCallbacks() const;

  void processErrorCallbacks() const;

};

} // namespace VideoDecodingAddon
